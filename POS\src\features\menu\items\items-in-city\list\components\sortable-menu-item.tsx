import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

import type { ItemsInCity } from '../../data'

interface SortableMenuItemProps {
  item: ItemsInCity
}

export function SortableMenuItem({ item }: SortableMenuItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: item.id as string
  })

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.6 : 1,
    zIndex: isDragging ? 1000 : 1
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className='flex cursor-move flex-col items-center rounded-lg border bg-white p-3 shadow-sm transition-shadow hover:shadow-md'
    >
      {item.image_path_thumb ? (
        <img
          src={item.image_path_thumb}
          alt={item.item_name}
          className='mb-2 h-16 w-16 rounded-md object-cover'
          onError={e => {
            const target = e.target as HTMLImageElement
            target.style.display = 'none'
          }}
        />
      ) : (
        <div className='mb-2 flex h-16 w-16 items-center justify-center rounded-md bg-gray-100'>
          <span className='text-xs text-gray-400'>No Image</span>
        </div>
      )}
      <h4 className='text-center text-xs font-medium text-gray-900 line-clamp-2'>{item.item_name}</h4>
      <p className='mt-1 text-xs text-gray-500'>
        {new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(item.ots_price || 0)}
      </p>
    </div>
  )
}

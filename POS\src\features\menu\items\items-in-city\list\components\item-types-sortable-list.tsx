import { useEffect, useMemo, useState } from 'react'

import { IconPinned, IconCaretUpDown } from '@tabler/icons-react'

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core'
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
  arrayMove
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'

type ItemTypeLite = { id: string; item_type_name: string; city_uid: string | null }

interface ItemTypesSortableListProps {
  itemTypes: Array<ItemTypeLite>
  selectedItemTypeUid: string
  onSelect: (id: string) => void
  className?: string
  onOrderChange?: (ids: string[]) => void
  onOrderChangeDetailed?: (ordered: ItemTypeLite[]) => void
}

export function ItemTypesSortableList({
  itemTypes,
  selectedItemTypeUid,
  onSelect,
  className,
  onOrderChange,
  onOrderChangeDetailed
}: ItemTypesSortableListProps) {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 6 }
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  const [sortedItemTypes, setSortedItemTypes] = useState<ItemTypeLite[]>([])

  useEffect(() => {
    if (itemTypes && itemTypes.length > 0) {
      setSortedItemTypes(
        itemTypes.map(t => ({ id: t.id, item_type_name: t.item_type_name, city_uid: t.city_uid ?? null }))
      )
    } else {
      setSortedItemTypes([])
    }
  }, [itemTypes])

  const ids = useMemo(() => sortedItemTypes.map(t => t.id), [sortedItemTypes])

  const handleItemTypesDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (!over || active.id === over.id) return

    setSortedItemTypes(items => {
      const activeIndex = items.findIndex(i => i.id === String(active.id))
      const overIndex = items.findIndex(i => i.id === String(over.id))
      if (activeIndex === -1 || overIndex === -1) return items

      const isEligible = (idx: number) => idx !== 0

      if (!isEligible(activeIndex)) return items

      const eligibleIndices: number[] = items.map((_, idx: number) => idx).filter((idx: number) => isEligible(idx))

      if (eligibleIndices.length <= 1) return items

      const oldEligiblePos = eligibleIndices.indexOf(activeIndex)
      if (oldEligiblePos === -1) return items

      let targetEligiblePos = eligibleIndices.findIndex(idx => idx >= overIndex)
      if (targetEligiblePos === -1) targetEligiblePos = eligibleIndices.length - 1
      
      // If trying to drop at index 0, move to next available position
      if (overIndex === 0) {
        targetEligiblePos = 0
      }

      if (!isEligible(overIndex)) {
        // If trying to drop on first item (index 0), find the closest eligible position
        let prevEligiblePos = -1
        for (let i = eligibleIndices.length - 1; i >= 0; i--) {
          if (eligibleIndices[i] < overIndex) {
            prevEligiblePos = i
            break
          }
        }
        const nextEligiblePos = eligibleIndices.findIndex((idx: number) => idx > overIndex)
        if (prevEligiblePos === -1 && nextEligiblePos === -1) return items
        if (prevEligiblePos === -1) targetEligiblePos = nextEligiblePos
        else if (nextEligiblePos === -1) targetEligiblePos = prevEligiblePos
        else {
          const prevDist = Math.abs(overIndex - eligibleIndices[prevEligiblePos])
          const nextDist = Math.abs(eligibleIndices[nextEligiblePos] - overIndex)
          targetEligiblePos = prevDist <= nextDist ? prevEligiblePos : nextEligiblePos
        }
      }

      if (oldEligiblePos === targetEligiblePos) return items

      const eligibleItems = eligibleIndices.map(idx => items[idx])
      const newEligibleItems = arrayMove(eligibleItems, oldEligiblePos, targetEligiblePos)

      const next = items.slice()
      let take = 0
      for (let i = 0; i < next.length; i++) {
        if (isEligible(i)) {
          next[i] = newEligibleItems[take++]
        }
      }
      
      // Ensure first item stays at index 0
      if (next.length > 0 && next[0].id !== items[0].id) {
        const firstItem = items[0]
        const otherItems = next.filter(item => item.id !== firstItem.id)
        next.splice(0, next.length, firstItem, ...otherItems)
      }

      if (onOrderChange) onOrderChange(next.map(i => i.id))
      if (onOrderChangeDetailed) onOrderChangeDetailed(next)
      return next
    })
  }

  return (
    <div className={className}>
      <ScrollArea className='h-[50vh] w-full'>
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleItemTypesDragEnd}>
          <SortableContext items={ids} strategy={verticalListSortingStrategy}>
            <div className='space-y-1 p-2'>
              {sortedItemTypes.map((itemType, index) => (
                <SortableItemTypeButton
                  key={itemType.id}
                  itemType={itemType}
                  isSelected={selectedItemTypeUid === itemType.id}
                  onSelect={() => onSelect(itemType.id)}
                  index={index}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
        <ScrollBar orientation='vertical' />
      </ScrollArea>
    </div>
  )
}

function SortableItemTypeButton({
  itemType,
  isSelected,
  onSelect,
  index
}: {
  itemType: ItemTypeLite
  isSelected: boolean
  onSelect: () => void
  index: number
}) {
  const isDraggable = index !== 0
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: itemType.id })

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.6 : 1,
    zIndex: isDragging ? 1000 : 1,
    cursor: isDraggable ? 'move' : 'default'
  }

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...(isDraggable ? listeners : {})}>
      <div
        onClick={onSelect}
        className={`w-full rounded p-2 text-left text-sm transition-colors ${
          isSelected ? 'bg-blue-100 text-blue-900' : 'hover:bg-gray-100'
        }`}
      >
        <div className='flex items-center gap-2'>
          {index === 0 ? (
            <IconPinned size={16} className='text-gray-600' />
          ) : (
            <IconCaretUpDown size={16} className='cursor-move text-gray-600' />
          )}
          <span>{itemType.item_type_name}</span>
        </div>
      </div>
    </div>
  )
}

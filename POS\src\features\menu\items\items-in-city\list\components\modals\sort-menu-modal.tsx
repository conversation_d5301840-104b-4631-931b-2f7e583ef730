import { useState, useEffect } from 'react'

import { KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core'
import { arrayMove, sortableKeyboardCoordinates } from '@dnd-kit/sortable'

import { usePosCitiesData } from '@/hooks/local-storage/use-pos-cities-data'
import { useItemTypesData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import type { ItemsInCity } from '../../../data'
import { useItemsInCityData, useBulkUpdateItemsInCity, useSortItemTypes } from '../../../hooks'
import { ItemTypesSortableList } from '../item-types-sortable-list'
import { MenuItemsSortablePanel } from '../menu-items-sortable-panel'

interface SortMenuModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SortMenuModal({ open, onOpenChange }: SortMenuModalProps) {
  const [selectedCityUid, setSelectedCityUid] = useState<string>('')
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('')
  const [orderedItemTypes, setOrderedItemTypes] = useState<
    Array<{ id: string; item_type_name: string; city_uid: string | null }>
  >([])
  const [itemTypesChanged, setItemTypesChanged] = useState(false)
  const [itemsChanged, setItemsChanged] = useState(false)

  // Fetch cities data
  const { cities } = usePosCitiesData()

  // Fetch item types (do not block by city; filter only when provided)
  const { data: itemTypes = [] } = useItemTypesData({
    ...(selectedCityUid && selectedCityUid !== 'all' ? { city_uid: selectedCityUid } : {}),
    enabled: open
  })

  const { data: menuItems = [] } = useItemsInCityData({
    params: {
      ...(selectedCityUid && { city_uid: selectedCityUid }),
      active: 1,
      skip_limit: true
    },
    enabled: open && !!selectedCityUid
  })

  // Bulk update hook
  const bulkUpdateMutation = useBulkUpdateItemsInCity()
  const sortItemTypesMutation = useSortItemTypes()

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  // Use state for sorted items to allow drag and drop
  const [sortedItems, setSortedItems] = useState<ItemsInCity[]>([])

  // Initialize and filter sorted items when source items or selection changes
  useEffect(() => {
    if (!selectedItemTypeUid) {
      setSortedItems([])
      setItemsChanged(false)
      return
    }

    const filtered = (menuItems || []).filter(item => item.item_type_uid === selectedItemTypeUid)
    if (filtered.length === 0) {
      setSortedItems([])
      setItemsChanged(false)
      return
    }

    const sorted = [...filtered].sort((a, b) => {
      if (a.sort !== b.sort) {
        return a.sort - b.sort
      }
      return a.id.localeCompare(b.id)
    })
    setSortedItems(sorted as any)
    setItemsChanged(false)
  }, [menuItems.length, selectedItemTypeUid]) // Only depend on length to avoid infinite loop

  // Reset selections when modal closes
  useEffect(() => {
    if (!open) {
      setSelectedCityUid('')
      setSelectedItemTypeUid('')
      setSortedItems([])
      setOrderedItemTypes([])
      setItemTypesChanged(false)
      setItemsChanged(false)
    }
  }, [open])

  // Reset item type selection when city changes
  useEffect(() => {
    setSelectedItemTypeUid('')
    setSortedItems([])
  }, [selectedCityUid])

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      setSortedItems(items => {
        const oldIndex = items.findIndex(item => item.id === active.id)
        const newIndex = items.findIndex(item => item.id === over.id)

        const next = arrayMove(items, oldIndex, newIndex)
        if (!itemsChanged) setItemsChanged(true)
        return next
      })
    }
  }

  const buildItemsUpdateData = () =>
    sortedItems.map((item, index) => ({
      id: item.id,
      item_id: item.item_id,
      item_name: item.item_name,
      description: item.description,
      ots_price: item.ots_price,
      ots_tax: item.ots_tax,
      ta_price: item.ta_price,
      ta_tax: item.ta_tax,
      time_sale_hour_day: item.time_sale_hour_day,
      time_sale_date_week: item.time_sale_date_week,
      allow_take_away: item.allow_take_away,
      is_eat_with: item.is_eat_with,
      image_path: item.image_path,
      image_path_thumb: item.image_path_thumb,
      item_color: item.item_color,
      list_order: index + 1,
      is_service: item.is_service,
      is_material: item.is_material,
      active: item.active,
      user_id: item.user_id,
      is_foreign: item.is_foreign,
      quantity_default: item.quantity_default,
      price_change: item.price_change,
      currency_type_id: item.currency_type_id,
      point: item.point,
      is_gift: item.is_gift,
      is_fc: item.is_fc,
      show_on_web: item.show_on_web,
      show_price_on_web: item.show_price_on_web,
      cost_price: item.cost_price,
      is_print_label: item.is_print_label,
      quantity_limit: item.quantity_limit,
      is_kit: item.is_kit,
      time_cooking: item.time_cooking,
      item_id_barcode: item.item_id_barcode,
      process_index: item.process_index,
      is_allow_discount: item.is_allow_discount,
      quantity_per_day: item.quantity_per_day,
      item_id_eat_with: item.item_id_eat_with,
      is_parent: item.is_parent,
      is_sub: item.is_sub,
      item_id_mapping: item.item_id_mapping,
      effective_date: item.effective_date,
      expire_date: item.expire_date,
      sort: index + 1,
      extra_data: {
        ...item.extra_data,
        formula_qrcode: ''
      },
      revision: item.revision,
      unit_uid: item.unit_uid,
      unit_secondary_uid: item.unit_secondary_uid,
      item_type_uid: item.item_type_uid,
      item_class_uid: item.item_class_uid,
      source_uid: item.source_uid,
      brand_uid: item.brand_uid,
      company_uid: item.company_uid,
      customization_uid: item.customization_uid,
      is_fabi: item.is_fabi,
      deleted: item.deleted,
      created_by: item.created_by,
      updated_by: item.updated_by,
      deleted_by: item.deleted_by,
      created_at: item.created_at,
      updated_at: item.updated_at,
      deleted_at: item.deleted_at,
      city_uid: item.city_uid
    }))

  const handleSave = () => {
    if (!selectedCityUid) {
      onOpenChange(false)
      return
    }

    const shouldSaveItemTypes = itemTypesChanged
    const shouldSaveItems = itemsChanged && sortedItems.length > 0 && !!selectedItemTypeUid

    if (!shouldSaveItemTypes && !shouldSaveItems) {
      onOpenChange(false)
      return
    }

    let finished = 0
    const total = Number(shouldSaveItemTypes) + Number(shouldSaveItems)
    const maybeClose = () => {
      finished += 1
      if (finished >= total) {
        onOpenChange(false)
      }
    }

    if (shouldSaveItemTypes) {
      const baseList: any[] = (itemTypes as any[]) || []
      const baseSorts: number[] = baseList.map(t => Number(t.sort ?? 0))

      const orderedLite = (orderedItemTypes.length > 0 ? orderedItemTypes : (itemTypes as any[])) || []

      const data = orderedLite.map((t: any, idx: number) => {
        const full = baseList.find(x => x.id === t.id) || t
        return {
          ...full,
          sort: baseSorts[idx]
        }
      })

      sortItemTypesMutation.mutate(
        { city_uid: selectedCityUid, data: data as any },
        { onSuccess: () => maybeClose() }
      )
    }

    if (shouldSaveItems) {
      const updateData = buildItemsUpdateData()
      bulkUpdateMutation.mutate(updateData as any, {
        onSuccess: () => maybeClose()
      })
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='flex h-[90vh] max-w-7xl flex-col sm:max-w-4xl'>
        <DialogHeader className='flex-shrink-0'>
          <DialogTitle className='text-center text-lg font-medium'>Sắp xếp thực đơn bán hàng</DialogTitle>
        </DialogHeader>

        {/* Warning message */}
        <div className='mb-4 flex-shrink-0 rounded-md border border-yellow-200 bg-yellow-50 p-3'>
          <p className='text-sm text-yellow-800'>
            Các món tại thành phố bị thay đổi vị trí sẽ chuyển thành món tại cửa hàng
          </p>
        </div>

        {/* City selection */}
        <div className='mb-4 flex flex-shrink-0 items-center gap-4'>
          <span className='text-sm font-medium whitespace-nowrap'>
            Thứ tự hiển thị sẽ được áp dụng trên thực đơn của thiết bị bán hàng
          </span>
          <div className='flex-1' />
          <div className='w-64'>
            <Select value={selectedCityUid} onValueChange={setSelectedCityUid}>
              <SelectTrigger>
                <SelectValue placeholder='Chọn thành phố' />
              </SelectTrigger>
              <SelectContent>
                {cities.map(city => (
                  <SelectItem key={city.id} value={city.id}>
                    {city.city_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Content area */}
        <div className='min-h-0 flex-1 overflow-hidden'>
          <div className='grid h-full grid-cols-12 gap-4'>
            {/* Left panel - Item Types */}
            <div className='col-span-3 h-full'>
              <div className='flex h-full flex-col rounded-lg border'>
                <div className='flex-shrink-0 border-b bg-gray-50 p-3'>
                  <h3 className='text-sm font-medium'>Tên nhóm món</h3>
                </div>
                <div className='min-h-0 flex-1'>
                  <ItemTypesSortableList
                    itemTypes={itemTypes as any}
                    selectedItemTypeUid={selectedItemTypeUid}
                    onSelect={id => setSelectedItemTypeUid(id)}
                    onOrderChangeDetailed={ordered => {
                      setOrderedItemTypes(ordered as any)
                      try {
                        const originalIds = (itemTypes as any[]).map((t: any) => t.id)
                        const newIds = (ordered as any[]).map((t: any) => t.id)
                        const changed =
                          originalIds.length !== newIds.length ||
                          originalIds.some((id: string, i: number) => id !== newIds[i])
                        setItemTypesChanged(changed)
                      } catch {}
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Right panel - Menu Items */}
            <div className='col-span-9 h-full'>
              <MenuItemsSortablePanel
                selectedItemTypeUid={selectedItemTypeUid}
                itemTypes={(itemTypes as any[]).map((t: any) => ({ id: t.id, item_type_name: t.item_type_name }))}
                sortedItems={sortedItems}
                sensors={sensors}
                onDragEnd={handleDragEnd}
                citySelected={!!selectedCityUid}
              />
            </div>
          </div>
        </div>

        {/* Footer buttons */}
        <div className='flex flex-shrink-0 justify-end gap-2 border-t pt-4'>
          <Button variant='outline' onClick={handleCancel} disabled={bulkUpdateMutation.isPending}>
            Hủy
          </Button>
          <Button
            onClick={handleSave}
            disabled={
              !selectedCityUid ||
              (!itemTypesChanged && !(itemsChanged && sortedItems.length > 0 && !!selectedItemTypeUid))
            }
          >
            {sortItemTypesMutation.isPending || bulkUpdateMutation.isPending ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

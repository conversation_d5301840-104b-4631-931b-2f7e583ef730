import React, { useState } from 'react'

import useDialogState from '@/hooks/ui/use-dialog-state'

import { ItemsInCity } from '../data'

type ItemsInCityDialogType = 'delete' | 'export' | 'import' | 'buffet-config' | 'export-dialog' | 'price-by-source-config' | 'sort-menu' | 'copy-menu'

interface ItemsInCityContextType {
  open: ItemsInCityDialogType | null
  setOpen: (str: ItemsInCityDialogType | null) => void
  currentRow: ItemsInCity | null
  setCurrentRow: React.Dispatch<React.SetStateAction<ItemsInCity | null>>
}

const ItemsInCityContext = React.createContext<ItemsInCityContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function ItemsInCityProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<ItemsInCityDialogType>(null)
  const [currentRow, setCurrentRow] = useState<ItemsInCity | null>(null)

  return (
    <ItemsInCityContext
      value={{
        open,
        setOpen,
        currentRow,
        setCurrentRow
      }}
    >
      {children}
    </ItemsInCityContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useItemsInCity = () => {
  const itemsInCityContext = React.useContext(ItemsInCityContext)

  if (!itemsInCityContext) {
    throw new Error('useItemsInCity has to be used within <ItemsInCityContext>')
  }

  return itemsInCityContext
}

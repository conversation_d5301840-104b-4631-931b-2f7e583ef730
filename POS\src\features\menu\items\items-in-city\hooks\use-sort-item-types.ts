import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'
import { useCurrentBrand } from '@/stores/posStore'

import { api } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface SortItemTypeFullPayloadItem {
  id: string
  item_type_id: string
  item_type_name: string
  sort: number
  sort_online?: number
  // Allow passthrough of any additional fields the API may require
  [key: string]: any
}

interface SortItemTypesPayload {
  city_uid: string
  data: Array<SortItemTypeFullPayloadItem>
}

export function useSortItemTypes() {
  const queryClient = useQueryClient()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  return useMutation({
    mutationFn: async (payload: SortItemTypesPayload) => {
      if (!company?.id || !selectedBrand?.id) {
        toast.error('Thiếu thông tin công ty hoặc thương hiệu')
        return
      }

      const body = payload.data.map(item => ({
        ...item,
        company_uid: company.id,
        brand_uid: selectedBrand.id,
        city_uid: payload.city_uid
      }))

      await api.put('/mdata/v1/item-types', body)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ITEM_TYPES] })
      toast.success('Sắp xếp nhóm món thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi sắp xếp nhóm món: ${error?.message}`)
    }
  })
}

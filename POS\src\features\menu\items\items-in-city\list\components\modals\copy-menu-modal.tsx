import { useEffect, useState } from 'react'

import { useAuthStore, useCurrentBrand } from '@/stores'
import type { City } from '@/types'
import { toast } from 'sonner'

import { useCitiesData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { MultiSelect } from '@/components/multi-select'

import { useCloneMenu } from '../../../items-in-store/hooks'
import { CopyMenuConfirmDialog } from './copy-menu-confirm-dialog'
import { MenuItemSelectionModal } from './menu-item-selection-modal'

interface CopyMenuModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CopyMenuModal({ open, onOpenChange }: CopyMenuModalProps) {
  const [sourceCityUid, setSourceCityUid] = useState<string>('')
  const [targetCityUids, setTargetCityUids] = useState<string[]>([])
  const [selectedMenuItems, setSelectedMenuItems] = useState<string[]>([])
  const [itemSelectionOpen, setItemSelectionOpen] = useState(false)
  const [confirmOpen, setConfirmOpen] = useState(false)

  const { data: cities = [], isLoading: isLoadingCities } = useCitiesData({
    enabled: open
  })

  const cloneMenuMutation = useCloneMenu()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  useEffect(() => {
    if (!open) {
      setSourceCityUid('')
      setTargetCityUids([])
      setSelectedMenuItems([])
      setItemSelectionOpen(false)
      setConfirmOpen(false)
    }
  }, [open])

  const cityOptions = cities.map((city: City) => ({
    label: city.name,
    value: city.id
  }))

  const targetCityOptions = cityOptions.filter(option => option.value !== sourceCityUid)

  const handleSelectMenuItems = () => {
    if (!sourceCityUid) {
      toast.error('Vui lòng chọn thành phố nguồn trước')
      return
    }
    setItemSelectionOpen(true)
  }

  const handleSync = () => {
    if (!sourceCityUid) {
      toast.error('Vui lòng chọn thành phố nguồn')
      return
    }
    if (targetCityUids.length === 0) {
      toast.error('Vui lòng chọn ít nhất một thành phố đích')
      return
    }
    if (selectedMenuItems.length === 0) {
      toast.error('Vui lòng chọn ít nhất một món ăn')
      return
    }
    setConfirmOpen(true)
  }

  const handleConfirmSync = async () => {
    try {
      if (!selectedBrand?.id) {
        toast.error('Thiếu thông tin công ty hoặc thương hiệu')
        return
      }

      for (const targetCityUid of targetCityUids) {
        await cloneMenuMutation.mutateAsync({
          company_uid: company?.id as string,
          brand_uid: selectedBrand.id,
          store_uid_root: sourceCityUid,
          store_uid_target: targetCityUid,
          list_item_id: selectedMenuItems,
          menu_type: 'city'
        })
      }

      toast.success('Đồng bộ thực đơn thành công!')
      setConfirmOpen(false)
      onOpenChange(false)
    } catch (error) {
      console.error('Error syncing menu:', error)
    }
  }

  const getSourceCityName = () => {
    const city = cities.find((s: City) => s.id === sourceCityUid)
    return city ? city.name : ''
  }

  const getTargetCityNames = () => {
    return targetCityUids
      .map(uid => {
        const city = cities.find((s: City) => s.id === uid)
        return city ? city.name : ''
      })
      .filter(Boolean)
      .join(', ')
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-2xl'>
          <DialogHeader>
            <DialogTitle>Đồng bộ thực đơn</DialogTitle>
          </DialogHeader>

          <div className='mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4'>
            <p className='text-sm text-yellow-800'>
              Nếu không chọn món, thực đơn tại thành phố đích sẽ bị xoá để đồng bộ dữ liệu!
            </p>
          </div>

          <div className='space-y-6'>
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Thành phố nguồn</label>
              <Select value={sourceCityUid} onValueChange={setSourceCityUid}>
                <SelectTrigger>
                  <SelectValue placeholder='Chọn thành phố' />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingCities ? (
                    <SelectItem disabled value='loading'>
                      Đang tải...
                    </SelectItem>
                  ) : (
                    cityOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className='space-y-2'>
              <label className='text-sm font-medium'>Thành phố đích</label>
              <MultiSelect
                options={targetCityOptions}
                value={targetCityUids}
                onValueChange={setTargetCityUids}
                placeholder='Tìm kiếm'
                variant='default'
                animation={2}
                maxCount={3}
              />
            </div>

            <div className='space-y-2'>
              <label className='text-sm font-medium'>Chọn món</label>
              <div className='flex gap-2'>
                <Button
                  variant='outline'
                  onClick={handleSelectMenuItems}
                  className='flex-1 justify-start text-left'
                  disabled={!sourceCityUid}
                >
                  {selectedMenuItems.length > 0 ? `Đã chọn ${selectedMenuItems.length} món` : 'Tất cả các món'}
                </Button>
                <Button onClick={handleSync} className='bg-green-600 hover:bg-green-700' disabled={cloneMenuMutation.isPending}>
                  {cloneMenuMutation.isPending ? 'Đang đồng bộ...' : 'Đồng bộ'}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <MenuItemSelectionModal
        open={itemSelectionOpen}
        onOpenChange={setItemSelectionOpen}
        sourceCityUid={sourceCityUid}
        selectedItems={selectedMenuItems}
        onItemsChange={setSelectedMenuItems}
        onSave={() => setItemSelectionOpen(false)}
      />

      <CopyMenuConfirmDialog
        open={confirmOpen}
        onOpenChange={setConfirmOpen}
        onConfirm={handleConfirmSync}
        title={`Bạn có muốn đồng bộ ${
          selectedMenuItems.length > 0 ? `${selectedMenuItems.length} món` : 'bộ thực đơn'
        } của thành phố ${getSourceCityName()} tới ${getTargetCityNames()}?`}
        isLoading={cloneMenuMutation.isPending}
      />
    </>
  )
}



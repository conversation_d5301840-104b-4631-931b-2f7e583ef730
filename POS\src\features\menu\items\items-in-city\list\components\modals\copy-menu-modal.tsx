import { useState, useEffect } from 'react'

import { useAuthStore, useCurrentBrand } from '@/stores'
import type { CityData } from '@/types/item-removed'
import { toast } from 'sonner'

import { useCitiesData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { MultiSelect } from '@/components/multi-select'

import { useCloneMenu } from '../../../hooks'
import { CopyMenuConfirmDialog } from './copy-menu-confirm-dialog'
import { MenuItemSelectionModal } from './menu-item-selection-modal'

interface CopyMenuModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CopyMenuModal({ open, onOpenChange }: CopyMenuModalProps) {
  const [sourceCityUid, setSourceCityUid] = useState<string>('')
  const [targetCityUids, setTargetCityUids] = useState<string[]>([])
  const [selectedMenuItems, setSelectedMenuItems] = useState<string[]>([])
  const [itemSelectionOpen, setItemSelectionOpen] = useState(false)
  const [confirmOpen, setConfirmOpen] = useState(false)

  const { data: cities = [], isLoading: isLoadingCities } = useCitiesData()

  const cloneMenuMutation = useCloneMenu()
  const { company } = useAuthStore(state => state.auth)
  const { selectedBrand } = useCurrentBrand()

  useEffect(() => {
    if (!open) {
      setSourceCityUid('')
      setTargetCityUids([])
      setSelectedMenuItems([])
      setItemSelectionOpen(false)
      setConfirmOpen(false)
    }
  }, [open])

  const cityOptions = cities.map((city: CityData) => ({
    label: city.city_name,
    value: city.id
  }))

  const targetCityOptions = cityOptions.filter(option => option.value !== sourceCityUid)

  const handleSelectMenuItems = () => {
    if (!sourceCityUid) {
      toast.error('Vui lòng chọn thành phố nguồn trước')
      return
    }
    setItemSelectionOpen(true)
  }

  const handleSync = () => {
    if (!sourceCityUid) {
      toast.error('Vui lòng chọn thành phố nguồn')
      return
    }
    if (targetCityUids.length === 0) {
      toast.error('Vui lòng chọn ít nhất một thành phố đích')
      return
    }
    if (selectedMenuItems.length === 0) {
      toast.error('Vui lòng chọn ít nhất một món ăn')
      return
    }
    setConfirmOpen(true)
  }

  const handleConfirmSync = async () => {
    try {
      if (!selectedBrand?.id) {
        toast.error('Thiếu thông tin công ty hoặc thương hiệu')
        return
      }

      for (const targetCityUid of targetCityUids) {
        await cloneMenuMutation.mutateAsync({
          company_uid: company?.id as string,
          brand_uid: selectedBrand.id,
          store_uid_root: sourceCityUid,
          store_uid_target: targetCityUid,
          list_item_id: selectedMenuItems,
          menu_type: 'city'
        })
      }

      toast.success('Đồng bộ thực đơn thành công!')
      setConfirmOpen(false)
      onOpenChange(false)
    } catch (error) {
      console.error('Error syncing menu:', error)
    }
  }

  const getSourceCityName = () => {
    const city = cities.find((s: CityData) => s.id === sourceCityUid)
    return city ? city.city_name : ''
  }

  const getTargetCityNames = () => {
    const targetCities = cities.filter((city: CityData) => targetCityUids.includes(city.id))
    if (targetCities.length === 1) {
      return targetCities[0].city_name
    }
    if (targetCities.length === 2) {
      return `${targetCities[0].city_name} và ${targetCities[1].city_name}`
    }
    if (targetCities.length > 2) {
      return `${targetCities[0].city_name} và ${targetCities.length - 1} thành phố khác`
    }
    return ''
  }

  if (isLoadingCities) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-2xl'>
          <div className='flex items-center justify-center p-8'>
            <div className='text-center'>
              <p className='text-sm text-muted-foreground'>Đang tải dữ liệu...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-2xl'>
          <DialogHeader>
            <DialogTitle>Đồng bộ thực đơn</DialogTitle>
          </DialogHeader>

          {/* Warning message */}
          <div className='mb-4 rounded-lg border border-yellow-200 bg-yellow-50 p-4'>
            <p className='text-sm text-yellow-800'>
              Nếu không chọn món, thực đơn tại thành phố đích sẽ bị xoá để đồng bộ dữ liệu!
            </p>
            <p className='mt-1 text-sm text-yellow-800'>
              Trước khi đồng bộ món, để đảm bảo dữ liệu tại thành phố{' '}
              <span className='font-semibold text-red-600'>đích</span>, hãy đồng bộ món tại thành phố{' '}
              <span className='font-semibold text-red-600'>nguồn</span>!
            </p>
          </div>

          <div className='space-y-4'>
            {/* Source City Selection */}
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Thành phố nguồn</label>
              <Select value={sourceCityUid} onValueChange={setSourceCityUid}>
                <SelectTrigger>
                  <SelectValue placeholder='Chọn thành phố nguồn' />
                </SelectTrigger>
                <SelectContent>
                  {cityOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Target Cities Selection */}
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Thành phố đích</label>
              <MultiSelect
                options={targetCityOptions}
                onValueChange={setTargetCityUids}
                defaultValue={targetCityUids}
                placeholder='Chọn thành phố đích'
                variant='inverted'
                animation={2}
                maxCount={3}
              />
            </div>

            {/* Menu Items Selection */}
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Chọn món</label>
              <div className='flex gap-2'>
                <Button
                  variant='outline'
                  onClick={handleSelectMenuItems}
                  className='flex-1 justify-start text-left'
                  disabled={!sourceCityUid}
                >
                  {selectedMenuItems.length > 0 ? `Đã chọn ${selectedMenuItems.length} món` : 'Tất cả các món'}
                </Button>
                <Button onClick={handleSync} className='bg-green-600 hover:bg-green-700' disabled={cloneMenuMutation.isPending}>
                  {cloneMenuMutation.isPending ? 'Đang đồng bộ...' : 'Đồng bộ'}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <MenuItemSelectionModal
        open={itemSelectionOpen}
        onOpenChange={setItemSelectionOpen}
        sourceCityUid={sourceCityUid}
        selectedItems={selectedMenuItems}
        onItemsChange={setSelectedMenuItems}
        onSave={() => setItemSelectionOpen(false)}
      />

      <CopyMenuConfirmDialog
        open={confirmOpen}
        onOpenChange={setConfirmOpen}
        onConfirm={handleConfirmSync}
        title={`Bạn có muốn đồng bộ ${
          selectedMenuItems.length > 0 ? `${selectedMenuItems.length} món` : 'bộ thực đơn'
        } của thành phố ${getSourceCityName()} tới ${getTargetCityNames()}?`}
        isLoading={cloneMenuMutation.isPending}
      />
    </>
  )
}


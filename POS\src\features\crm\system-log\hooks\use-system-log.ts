import { useState, useMemo, useCallback, useEffect } from 'react'

import { format } from 'date-fns'

import { useCurrentBrand } from '@/stores/posStore'

import { systemLogApi, formatStartDateForApi, formatEndDateForApi } from '@/lib/api/crm'

import { createTextFilter } from '@/utils/date-filters'

import { DEFAULT_DATE_RANGE } from '@/constants/crm'

import { getActionLabel, SYSTEM_LOG_FILTER_OPTIONS } from '../constants/action-mappings'
import type { SystemLogFilters, SystemLog } from '../data'
import { ALL_MOCK_LOGS } from '../data'

export function useSystemLog() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [apiLogs, setApiLogs] = useState<SystemLog[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const { selectedBrand } = useCurrentBrand()
  const [filters, setFilters] = useState<SystemLogFilters>({
    dateRange: {
      from: DEFAULT_DATE_RANGE.FROM,
      to: DEFAULT_DATE_RANGE.TO
    },
    request_path: 'all',
    user_name: ''
  })

  // Filter logs based on current filters
  const filteredLogs = useMemo(() => {
    const sourceData = apiLogs.length > 0 ? apiLogs : ALL_MOCK_LOGS
    let filtered = [...sourceData]

    // Filter by Vietnamese action label instead of raw request_path
    if (filters.request_path && filters.request_path !== 'all') {
      filtered = filtered.filter(log => {
        const actionLabel = getActionLabel(log.request_path)
        return actionLabel === filters.request_path
      })
    }

    const userFilter = createTextFilter<SystemLog>('user_name')
    filtered = filtered.filter(userFilter(filters.user_name))

    return filtered.sort((a, b) => new Date(b.request_at).getTime() - new Date(a.request_at).getTime())
  }, [filters, apiLogs])

  const updateFilter = (key: keyof SystemLogFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const updateDateRange = (from: Date | null, to: Date | null) => {
    setFilters(prev => ({
      ...prev,
      dateRange: { from, to }
    }))
  }

  const fetchSystemLogs = useCallback(async () => {
    if (!filters.dateRange.from || !filters.dateRange.to) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const firstResponse = await systemLogApi.getSystemLogs({
        start_time: formatStartDateForApi(filters.dateRange.from),
        end_time: formatEndDateForApi(filters.dateRange.to),
        pos_parent: selectedBrand?.brandId || '',
        page: 1
      })

      if (!firstResponse.data || !firstResponse.data.data) {
        setApiLogs([])
        setTotalCount(0)
        return
      }

      const allLogs = [...firstResponse.data.data]
      const totalPages = firstResponse.data.totalPage || 1
      setTotalCount(firstResponse.data.count || 0)

      if (totalPages > 1) {
        const pagePromises = []
        for (let page = 2; page <= totalPages; page++) {
          pagePromises.push(
            systemLogApi.getSystemLogs({
              start_time: formatStartDateForApi(filters.dateRange.from),
              end_time: formatEndDateForApi(filters.dateRange.to),
              pos_parent: selectedBrand?.brandId || '',
              page
            })
          )
        }

        const pageResponses = await Promise.all(pagePromises)
        pageResponses.forEach(response => {
          if (response.data && response.data.data) {
            allLogs.push(...response.data.data)
          }
        })
      }

      setApiLogs(allLogs)
    } catch (err) {
      console.error('Error fetching system logs:', err)
      setError('Có lỗi xảy ra khi tải nhật ký hệ thống')
      setApiLogs(ALL_MOCK_LOGS)
      setTotalCount(ALL_MOCK_LOGS.length)
    } finally {
      setIsLoading(false)
    }
  }, [filters.dateRange.from, filters.dateRange.to])

  const searchLogs = useCallback(async () => {
    await fetchSystemLogs()
  }, [fetchSystemLogs])

  useEffect(() => {
    fetchSystemLogs()
  }, [fetchSystemLogs])

  const availableRequestPaths = useMemo(() => {
    return SYSTEM_LOG_FILTER_OPTIONS.map(option => ({
      value: option,
      label: option
    }))
  }, [])

  const availableUsers = useMemo(() => {
    const dataSource = apiLogs.length > 0 ? apiLogs : ALL_MOCK_LOGS
    const users = Array.from(new Set(dataSource.map(log => log.user_name)))
    return users.map(user => ({ value: user, label: user }))
  }, [apiLogs])

  const formatTimestamp = useCallback((timestamp: string) => {
    try {
      const date = new Date(timestamp)
      return format(date, 'dd/MM/yyyy HH:mm:ss')
    } catch {
      return timestamp
    }
  }, [])

  return {
    logs: filteredLogs,
    isLoading,
    error,
    filters,
    updateFilter,
    updateDateRange,
    searchLogs,
    availableRequestPaths,
    availableUsers,
    formatTimestamp,
    totalCount: totalCount || filteredLogs.length
  }
}

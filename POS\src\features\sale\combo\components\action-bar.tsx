import { useState } from 'react'

import { Settings, Plus, Download, Upload, ArrowUpDown, Search as SearchIcon } from 'lucide-react'

import { useComboPromotionsData, useStoresData } from '@/hooks/api'

import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui'

import { ComboSortModal } from '@/features/combos/components/combo-sort-modal'

interface ComboActionBarProps {
  searchTerm: string
  setSearchTerm: (value: string) => void
  selectedStore: string
  setSelectedStore: (value: string) => void
  selectedPromotion: string
  setSelectedPromotion: (value: string) => void
  expiryStatus: 'all' | 'unexpired' | 'expired'
  setExpiryStatus: (value: 'all' | 'unexpired' | 'expired') => void
  onSearchKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void
  onCreateCombo: () => void
  onExportCombo: () => void
  onImportCombo: () => void
}

export function ComboActionBar({
  searchTerm,
  setSearchTerm,
  selectedStore,
  setSelectedStore,
  selectedPromotion,
  setSelectedPromotion,
  expiryStatus,
  setExpiryStatus,
  onSearchKeyDown,
  onCreateCombo,
  onExportCombo,
  onImportCombo
}: ComboActionBarProps) {
  const [isSortModalOpen, setIsSortModalOpen] = useState(false)
  const { data: stores = [] } = useStoresData()
  const { data: promotions = [] } = useComboPromotionsData({
    storeUid: selectedStore === 'all' ? undefined : selectedStore
  })

  const handleSortCombo = () => {
    setIsSortModalOpen(true)
  }

  const storeUids = stores.map(store => store.id)

  return (
    <div className='space-y-4'>
      <div className='flex flex-wrap items-center gap-4'>
        <h1 className='text-2xl font-semibold'>Combo</h1>

        <div className='relative max-w-[300px] min-w-[200px] flex-1'>
          <SearchIcon className='text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2' />
          <Input
            placeholder='Tìm kiếm combo (nhấn Enter để tìm)'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            onKeyDown={onSearchKeyDown}
            className='pl-9'
          />
        </div>

        <Select value={selectedStore} onValueChange={setSelectedStore}>
          <SelectTrigger className='w-[180px]'>
            <SelectValue placeholder='Chọn cửa hàng' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả cửa hàng</SelectItem>
            {stores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedPromotion} onValueChange={setSelectedPromotion}>
          <SelectTrigger className='w-[180px]'>
            <SelectValue placeholder='Chọn khuyến mãi' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả khuyến mãi</SelectItem>
            {promotions.map(promotion => (
              <SelectItem key={promotion.promotion_id} value={promotion.promotion_id}>
                {promotion.promotion_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={expiryStatus} onValueChange={(value: 'all' | 'unexpired' | 'expired') => setExpiryStatus(value)}>
          <SelectTrigger className='w-[180px]'>
            <SelectValue placeholder='Trạng thái' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả ngày áp dụng</SelectItem>
            <SelectItem value='expired'>Hết hạn</SelectItem>
            <SelectItem value='unexpired'>Chưa hết hạn</SelectItem>
          </SelectContent>
        </Select>

        <div className='ml-auto flex items-center gap-2'>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='outline' className='flex items-center gap-2'>
                <Settings className='h-4 w-4' />
                Tiện ích
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onClick={onExportCombo}>
                <Download className='mr-2 h-4 w-4' />
                Xuất, sửa combo
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onImportCombo}>
                <Upload className='mr-2 h-4 w-4' />
                Thêm combo từ file
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSortCombo}>
                <ArrowUpDown className='mr-2 h-4 w-4' />
                Sắp xếp combo
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button onClick={onCreateCombo} className='flex items-center gap-2'>
            <Plus className='h-4 w-4' />
            Tạo combo
          </Button>
        </div>
      </div>

      <ComboSortModal open={isSortModalOpen} onOpenChange={setIsSortModalOpen} storeUids={storeUids} />
    </div>
  )
}
